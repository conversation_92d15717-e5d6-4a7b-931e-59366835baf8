const { getDefaultConfig, mergeConfig } = require('@react-native/metro-config');
const path = require('path');

/**
 * Metro configuration for Turborepo monorepo
 * Optimized for React Native 0.77+ with symlink support
 * https://reactnative.dev/docs/metro
 *
 * @type {import('metro-config').MetroConfig}
 */

// Get the default Metro config
const defaultConfig = getDefaultConfig(__dirname);

// Monorepo root
const monorepoRoot = path.resolve(__dirname, '../..');

const config = {
  projectRoot: __dirname,

  // Watch folders for monorepo packages
  watchFolders: [
    path.resolve(monorepoRoot, 'packages'),
  ],

  resolver: {
    // Node modules resolution paths - critical for monorepo
    nodeModulesPaths: [
      path.resolve(monorepoRoot, 'node_modules'),
      path.resolve(__dirname, 'node_modules'),
    ],

    // Force specific modules to resolve from monorepo root
    extraNodeModules: {
      '@babel/runtime': path.resolve(monorepoRoot, 'node_modules/@babel/runtime'),
      'react': path.resolve(monorepoRoot, 'node_modules/react'),
      'react-native': path.resolve(monorepoRoot, 'node_modules/react-native'),
    },

    // Resolver main fields for React Native
    resolverMainFields: ['react-native', 'browser', 'main'],

    // Disable hierarchical lookup to force using our paths
    disableHierarchicalLookup: false,
  },

  transformer: {
    // Use default transformer settings
    ...defaultConfig.transformer,
    getTransformOptions: async () => ({
      transform: {
        experimentalImportSupport: false,
        inlineRequires: true,
      },
    }),
  },
};

module.exports = mergeConfig(defaultConfig, config);
