const { getDefaultConfig, mergeConfig } = require('@react-native/metro-config');
const path = require('path');

/**
 * Metro configuration for Turborepo monorepo
 * Based on React Native 0.72+ official symlink support
 * Sources: React Native docs, Medium articles, GitHub issues
 *
 * @type {import('metro-config').MetroConfig}
 */

// Monorepo root directory
const monorepoRoot = path.resolve(__dirname, '../..');

const config = {
  projectRoot: __dirname,

  // Watch folders - critical for monorepo
  watchFolders: [
    path.resolve(monorepoRoot, 'packages'),
    path.resolve(monorepoRoot, 'node_modules'),
  ],

  resolver: {
    // CRITICAL: Enable symlink support (React Native 0.72+)
    unstable_enableSymlinks: true,

    // CRITICAL: Enable package exports support (React Native 0.72+)
    unstable_enablePackageExports: true,

    // Node modules resolution paths
    nodeModulesPaths: [
      path.resolve(monorepoRoot, 'node_modules'),
      path.resolve(__dirname, 'node_modules'),
    ],

    // Force specific modules to resolve from monorepo root
    extraNodeModules: {
      '@babel/runtime': path.resolve(monorepoRoot, 'node_modules/@babel/runtime'),
      'react': path.resolve(monorepoRoot, 'node_modules/react'),
      'react-native': path.resolve(monorepoRoot, 'node_modules/react-native'),
    },

    // Resolver main fields
    resolverMainFields: ['react-native', 'browser', 'main'],

    // Disable hierarchical lookup for better control
    disableHierarchicalLookup: false,
  },

  transformer: {
    getTransformOptions: async () => ({
      transform: {
        experimentalImportSupport: false,
        inlineRequires: true,
      },
    }),
  },
};

module.exports = mergeConfig(getDefaultConfig(__dirname), config);
